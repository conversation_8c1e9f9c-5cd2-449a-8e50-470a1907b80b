-- 医疗救治系统客户端

local lifeExtended = false
local lifeExtensionTimer = 0

-- 应用生命延长效果
RegisterNetEvent('organ_trade:applyLifeExtension', function(duration)
    lifeExtended = true
    lifeExtensionTimer = GetGameTimer() + duration
    
    -- 显示生命延长UI
    CreateThread(function()
        while lifeExtended and GetGameTimer() < lifeExtensionTimer do
            local remainingTime = math.max(0, lifeExtensionTimer - GetGameTimer())
            local minutes = math.floor(remainingTime / 60000)
            local seconds = math.floor((remainingTime % 60000) / 1000)
            
            -- 显示倒计时
            SetTextFont(4)
            SetTextProportional(1)
            SetTextScale(0.5, 0.5)
            SetTextColour(255, 100, 100, 255)
            SetTextDropShadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString(string.format("肾上腺素效果: %02d:%02d", minutes, seconds))
            DrawText(0.85, 0.05)
            
            -- 最后30秒闪烁警告
            if remainingTime <= 30000 then
                if math.floor(GetGameTimer() / 500) % 2 == 0 then
                    DrawRect(0.5, 0.5, 1.0, 1.0, 255, 0, 0, 20)
                end
            end
            
            Wait(100)
        end
    end)
    
    ESX.ShowNotification('肾上腺素生效，你的生命得到延长', 'success')
end)

-- 移除生命延长效果
RegisterNetEvent('organ_trade:removeLifeExtension', function()
    lifeExtended = false
    lifeExtensionTimer = 0
    ESX.ShowNotification('肾上腺素效果结束', 'error')
end)

-- 传送到医院
RegisterNetEvent('organ_trade:teleportToHospital', function(hospital)
    local playerPed = PlayerPedId()
    SetEntityCoords(playerPed, hospital.x, hospital.y, hospital.z, false, false, false, true)
    
    -- 淡入淡出效果
    DoScreenFadeOut(1000)
    Wait(1000)
    DoScreenFadeIn(1000)
end)

-- 显示手术选项
RegisterNetEvent('organ_trade:showSurgeryOptions', function(targetId, missingOrgans)
    local elements = {}
    
    table.insert(elements, {
        label = '=== 器官修复手术 ===',
        value = nil
    })
    
    for _, organ in ipairs(missingOrgans) do
        table.insert(elements, {
            label = string.format('修复 %s - $%d', organ.name, organ.cost),
            value = organ.field,
            cost = organ.cost
        })
    end
    
    table.insert(elements, {
        label = '取消手术',
        value = 'cancel'
    })
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'surgery_repair_menu', {
        title = '器官修复手术',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        if data.current.value == 'cancel' or not data.current.value then
            menu.close()
            return
        end
        
        local organField = data.current.value
        local cost = data.current.cost
        
        menu.close()
        
        -- 确认手术
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'surgery_repair_confirm', {
            title = string.format('确认进行器官修复手术？\n费用: $%d', cost)
        }, function(data2, menu2)
            if data2.value == 'yes' then
                TriggerServerEvent('organ_trade:repairOrgan', targetId, organField, cost)
            end
            menu2.close()
        end, function(data2, menu2)
            menu2.close()
        end)
        
    end, function(data, menu)
        menu.close()
    end)
end)

-- 使用手术台
function UseSurgeryTable()
    -- 获取附近需要治疗的患者
    TriggerServerEvent('organ_trade:getNearbyPatients')
end

-- 接收附近患者列表
RegisterNetEvent('organ_trade:receiveNearbyPatients', function(patients)
    local elements = {}
    
    -- 添加自我治疗选项
    table.insert(elements, {
        label = '自我检查',
        value = 'self'
    })
    
    -- 添加其他患者
    for _, patient in ipairs(patients) do
        table.insert(elements, {
            label = string.format('%s (距离: %.1fm)', patient.name, patient.distance),
            value = patient.id
        })
    end
    
    if #elements == 1 then
        -- 只有自我治疗选项
        TriggerServerEvent('organ_trade:useSurgeryTable', nil)
        return
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'surgery_patient_menu', {
        title = '选择患者',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local targetId = data.current.value == 'self' and nil or data.current.value
        menu.close()
        
        TriggerServerEvent('organ_trade:useSurgeryTable', targetId)
    end, function(data, menu)
        menu.close()
    end)
end)

-- 创建医院标记
CreateThread(function()
    for _, hospital in ipairs(Config.Medical.hospital_locations) do
        local blip = AddBlipForCoord(hospital.x, hospital.y, hospital.z)
        SetBlipSprite(blip, 61)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(hospital.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- 创建手术台标记和交互
CreateThread(function()
    for i, surgeryTable in ipairs(Config.Medical.surgery_table) do
        -- 创建标记
        local blip = AddBlipForCoord(surgeryTable.x, surgeryTable.y, surgeryTable.z)
        SetBlipSprite(blip, 403)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.6)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString('手术台')
        EndTextCommandSetBlipName(blip)
        
        -- 创建交互点
        CreateThread(function()
            while true do
                local playerCoords = GetEntityCoords(PlayerPedId())
                local distance = #(playerCoords - surgeryTable)
                
                if distance <= 3.0 then
                    -- 显示提示
                    SetTextComponentFormat('STRING')
                    AddTextComponentString('按 ~INPUT_CONTEXT~ 使用手术台')
                    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                    
                    if IsControlJustPressed(0, 38) then -- E键
                        UseSurgeryTable()
                    end
                end
                
                Wait(distance <= 10.0 and 100 or 1000)
            end
        end)
    end
end)

-- 医护人员命令
RegisterCommand('surgery', function()
    UseSurgeryTable()
end)

-- 添加建议文本
TriggerEvent('chat:addSuggestion', '/surgery', '使用手术台（医护人员）')

-- 导出函数
exports('UseSurgeryTable', UseSurgeryTable)
exports('IsLifeExtended', function() return lifeExtended end)

print('^2[器官交易系统] ^7医疗救治系统客户端已加载')
