-- 迷药系统客户端

local isDrugged = false
local drugEffectActive = false
local originalWalkSpeed = 1.0
local originalRunSpeed = 1.0

-- 应用迷药效果
RegisterNetEvent('organ_trade:applyDrugEffect', function(duration)
    if isDrugged then return end
    
    isDrugged = true
    drugEffectActive = true
    
    -- 保存原始移动速度
    local playerPed = PlayerPedId()
    originalWalkSpeed = GetPedMoveRateOverride(playerPed)
    originalRunSpeed = GetPedMoveRateOverride(playerPed)
    
    -- 应用视觉效果
    ApplyDrugVisualEffects()
    
    -- 限制移动速度
    CreateThread(function()
        while drugEffectActive do
            local playerPed = PlayerPedId()
            
            -- 设置缓慢移动
            SetPedMoveRateOverride(playerPed, Config.Drug.crawl_speed)
            SetRunSprintMultiplierForPlayer(PlayerId(), Config.Drug.crawl_speed)
            
            -- 禁用跳跃和攀爬
            DisableControlAction(0, 22, true)  -- 跳跃
            DisableControlAction(0, 23, true)  -- 进入载具
            DisableControlAction(0, 75, true)  -- 离开载具
            
            -- 强制爬行动画
            if not IsEntityPlayingAnim(playerPed, Config.Drug.animation.dict, Config.Drug.animation.name, 3) then
                RequestAnimDict(Config.Drug.animation.dict)
                while not HasAnimDictLoaded(Config.Drug.animation.dict) do
                    Wait(100)
                end
                
                TaskPlayAnim(playerPed, Config.Drug.animation.dict, Config.Drug.animation.name, 8.0, -8.0, -1, 1, 0, false, false, false)
            end
            
            Wait(100)
        end
    end)
    
    -- 设置效果持续时间
    CreateThread(function()
        Wait(duration)
        RemoveDrugEffect()
    end)
end)

-- 移除迷药效果
RegisterNetEvent('organ_trade:removeDrugEffect', function()
    RemoveDrugEffect()
end)

-- 移除迷药效果函数
function RemoveDrugEffect()
    if not isDrugged then return end
    
    isDrugged = false
    drugEffectActive = false
    
    local playerPed = PlayerPedId()
    
    -- 恢复移动速度
    SetPedMoveRateOverride(playerPed, originalWalkSpeed)
    SetRunSprintMultiplierForPlayer(PlayerId(), originalRunSpeed)
    
    -- 停止动画
    ClearPedTasks(playerPed)
    
    -- 移除视觉效果
    RemoveDrugVisualEffects()

    ESX.ShowNotification('药效已消退...', 'info')

-- 应用视觉效果
function ApplyDrugVisualEffects()
    CreateThread(function()
        while drugEffectActive do
            -- 屏幕效果
            SetTimecycleModifier('spectator5')
            SetTimecycleModifierStrength(0.3)
            
            -- 摇晃效果
            ShakeGameplayCam('DRUNK_SHAKE', 0.5)
            
            Wait(100)
        end
    end)
end

-- 移除视觉效果
function RemoveDrugVisualEffects()
    -- 清除屏幕效果
    ClearTimecycleModifier()
    
    -- 停止摇晃
    StopGameplayCamShaking(true)
end

-- 传送玩家
RegisterNetEvent('organ_trade:teleportPlayer', function(coords)
    if not isDrugged then return end
    
    local playerPed = PlayerPedId()
    SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
    
    ESX.ShowNotification('你被带到了一个陌生的地方...', 'error')
end)

-- 使用迷药道具
function UseDrugItem()
    -- 获取附近玩家
    TriggerServerEvent('organ_trade:getNearbyPlayers')
end

-- 接收附近玩家列表
RegisterNetEvent('organ_trade:receiveNearbyPlayers', function(players)
    if #players == 0 then
        ESX.ShowNotification('附近没有玩家', 'error')
        return
    end
    
    -- 创建玩家选择菜单
    local elements = {}
    
    for _, player in ipairs(players) do
        table.insert(elements, {
            label = string.format('%s (距离: %.1fm)', player.name, player.distance),
            value = player.id
        })
    end
    
    ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'drug_target_menu', {
        title = '选择目标',
        align = 'top-left',
        elements = elements
    }, function(data, menu)
        local targetId = data.current.value
        menu.close()
        
        -- 确认对话框
        ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'drug_confirm', {
            title = '确认使用迷药？'
        }, function(data2, menu2)
            if data2.value == 'yes' then
                TriggerServerEvent('organ_trade:useDrug', targetId)
            end
            menu2.close()
        end, function(data2, menu2)
            menu2.close()
        end)
        
    end, function(data, menu)
        menu.close()
    end)
end)

-- 检查迷药状态
function CheckDrugStatus()
    TriggerServerEvent('organ_trade:checkDrugStatus')
end

-- 接收迷药状态响应
RegisterNetEvent('organ_trade:drugStatusResponse', function(drugged)
    isDrugged = drugged
    if drugged and not drugEffectActive then
        -- 重新应用效果（用于重连后恢复状态）
        drugEffectActive = true
        ApplyDrugVisualEffects()
    end
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000) -- 等待ESX加载
        CheckDrugStatus()
    end)
end)

-- 导出函数
exports('UseDrugItem', UseDrugItem)
exports('IsDrugged', function() return isDrugged end)

print('^2[器官交易系统] ^7迷药系统客户端已加载')
